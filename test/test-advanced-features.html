<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级功能测试页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .rainbow-text {
            background: linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: rainbow 3s ease-in-out infinite;
        }
        @keyframes rainbow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-blue-400 mb-8">高级功能测试</h1>
        
        <!-- 测试区域1：顶部信息栏固定头部 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-blue-400 mb-4">1. 顶部信息栏头部冻结模式</h2>
            <div class="space-y-2">
                <div class="text-sm text-gray-300">✅ 修改内容：将顶部信息栏设置为固定头部，页面滚动时保持不变</div>
                <div class="text-sm text-yellow-300">📋 测试方法：滚动页面，观察顶部信息栏是否保持固定</div>
                <div class="text-sm text-cyan-300">🔧 实现特点：</div>
                <ul class="text-sm text-cyan-300 list-disc list-inside ml-4">
                    <li>使用 fixed 定位固定在页面顶部</li>
                    <li>添加占位符避免内容被遮挡</li>
                    <li>在任何模式下都保持固定</li>
                </ul>
            </div>
        </div>

        <!-- 测试区域2：Mock北京时间功能 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-yellow-400 mb-4">2. Mock"北京时间"功能</h2>
            <div class="space-y-2">
                <div class="text-sm text-gray-300">✅ 修改内容：增加调试时间功能，用于彩排或调试</div>
                <div class="text-sm text-yellow-300">📋 测试方法：点击顶部的"北京时间"按钮</div>
                <div class="text-sm text-cyan-300">🕐 功能特点：</div>
                <ul class="text-sm text-cyan-300 list-disc list-inside ml-4">
                    <li>点击"北京时间"可设置调试时间</li>
                    <li>调试模式下显示"调试时间"</li>
                    <li>时钟显示为<span class="rainbow-text">彩虹色渐变</span></li>
                    <li>系统内所有产品基准以调试时间为准</li>
                    <li>刷新页面不会退出调试模式</li>
                    <li>点击"调试时间"可退出测试模式</li>
                </ul>
            </div>
        </div>

        <!-- 测试区域3：加载配置双模式 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-purple-400 mb-4">3. "加载配置"双模式</h2>
            <div class="space-y-2">
                <div class="text-sm text-gray-300">✅ 修改内容：加载配置按钮变为"本地文件"和"线上配置"两种模式</div>
                <div class="text-sm text-yellow-300">📋 测试方法：在配置管理中查看两个加载按钮</div>
                <div class="text-sm text-cyan-300">🌐 线上配置特点：</div>
                <ul class="text-sm text-cyan-300 list-disc list-inside ml-4">
                    <li>支持输入URL地址读取配置</li>
                    <li>支持本地文件上传至线上的直链</li>
                    <li>支持Github raw文件链接</li>
                    <li>自动验证URL格式和配置文件格式</li>
                </ul>
            </div>
        </div>

        <!-- 测试区域4：添加产品后锚点跳转 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-green-400 mb-4">4. 添加产品后锚点跳转</h2>
            <div class="space-y-2">
                <div class="text-sm text-gray-300">✅ 修改内容：添加产品后自动跳转到配置列表页面锚点</div>
                <div class="text-sm text-yellow-300">📋 测试方法：添加一个产品配置，观察页面跳转</div>
                <div class="text-sm text-cyan-300">🎯 跳转特点：</div>
                <ul class="text-sm text-cyan-300 list-disc list-inside ml-4">
                    <li>成功添加产品后自动滚动到配置列表</li>
                    <li>使用平滑滚动动画</li>
                    <li>提升用户体验</li>
                </ul>
            </div>
        </div>

        <!-- 测试数据示例 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-pink-400 mb-4">5. 测试数据和URL示例</h2>
            <div class="space-y-4">
                <div>
                    <h3 class="text-lg font-semibold text-purple-400 mb-2">线上配置URL示例：</h3>
                    <div class="bg-gray-700 p-3 rounded text-sm font-mono text-gray-300">
https://raw.githubusercontent.com/username/repo/main/config.ksconf
https://example.com/path/to/config.ksconf
https://cdn.example.com/configs/livestream_config.json
                    </div>
                    <div class="text-xs text-gray-400 mt-2">
                        注意：URL必须能直接访问并返回JSON格式的配置文件
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold text-yellow-400 mb-2">调试时间测试场景：</h3>
                    <div class="bg-gray-700 p-3 rounded text-sm text-gray-300">
                        <strong>场景1：</strong>设置调试时间为当前时间+10分钟，测试即将上线提醒<br>
                        <strong>场景2：</strong>设置调试时间为产品上线时间，测试上线中状态<br>
                        <strong>场景3：</strong>设置调试时间为产品结束时间，测试已下线状态
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试步骤 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-cyan-400 mb-4">6. 详细测试步骤</h2>
            <div class="space-y-4">
                <div>
                    <h3 class="text-lg font-semibold text-blue-400 mb-2">固定头部测试：</h3>
                    <ol class="text-sm text-gray-300 list-decimal list-inside space-y-1">
                        <li>打开主应用</li>
                        <li>向下滚动页面</li>
                        <li>验证顶部信息栏是否保持固定</li>
                        <li>切换不同模式，验证头部固定效果</li>
                    </ol>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold text-yellow-400 mb-2">调试时间测试：</h3>
                    <ol class="text-sm text-gray-300 list-decimal list-inside space-y-1">
                        <li>点击顶部的"北京时间"按钮</li>
                        <li>设置一个调试时间</li>
                        <li>验证时钟是否变为彩虹色</li>
                        <li>验证标签是否变为"调试时间"</li>
                        <li>添加产品配置，验证时间基准是否正确</li>
                        <li>刷新页面，验证调试模式是否保持</li>
                        <li>点击"调试时间"退出测试模式</li>
                    </ol>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold text-purple-400 mb-2">线上配置测试：</h3>
                    <ol class="text-sm text-gray-300 list-decimal list-inside space-y-1">
                        <li>切换到配置管理</li>
                        <li>点击"加载线上配置"按钮</li>
                        <li>输入一个有效的配置文件URL</li>
                        <li>点击"读取在线配置"</li>
                        <li>验证配置是否成功加载</li>
                        <li>测试无效URL的错误处理</li>
                    </ol>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold text-green-400 mb-2">锚点跳转测试：</h3>
                    <ol class="text-sm text-gray-300 list-decimal list-inside space-y-1">
                        <li>在配置管理中添加一个新产品</li>
                        <li>填写完整的产品信息</li>
                        <li>点击"添加产品"按钮</li>
                        <li>观察页面是否自动滚动到配置列表</li>
                        <li>验证新添加的产品是否在列表中显示</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- 测试按钮 -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-xl font-semibold text-blue-400 mb-4">开始测试</h2>
            <div class="space-x-4">
                <button onclick="window.open('../index.html', '_blank')" 
                        class="bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded transition-colors">
                    打开主应用测试
                </button>
                <button onclick="testRainbowEffect()" 
                        class="bg-yellow-600 hover:bg-yellow-700 px-6 py-2 rounded transition-colors">
                    预览彩虹效果
                </button>
                <button onclick="copyTestUrl()" 
                        class="bg-purple-600 hover:bg-purple-700 px-6 py-2 rounded transition-colors">
                    复制测试URL
                </button>
                <button onclick="showTestChecklist()" 
                        class="bg-green-600 hover:bg-green-700 px-6 py-2 rounded transition-colors">
                        显示测试清单
                </button>
            </div>
        </div>

        <!-- 彩虹效果演示 -->
        <div id="rainbowDemo" class="hidden bg-gray-800 rounded-lg p-6 mt-6">
            <h2 class="text-xl font-semibold mb-4">彩虹效果演示</h2>
            <div class="text-center">
                <div class="text-4xl font-mono rainbow-text">12:34:56</div>
                <p class="text-sm text-gray-400 mt-2">这就是调试时间模式下的彩虹色效果</p>
            </div>
        </div>
    </div>

    <script>
        function testRainbowEffect() {
            const demo = document.getElementById('rainbowDemo');
            demo.classList.toggle('hidden');
        }

        function copyTestUrl() {
            const testUrl = 'https://raw.githubusercontent.com/example/repo/main/test-config.ksconf';
            navigator.clipboard.writeText(testUrl).then(() => {
                alert('测试URL已复制到剪贴板！\n\n' + testUrl);
            }).catch(() => {
                alert('复制失败，请手动复制：\n\n' + testUrl);
            });
        }

        function showTestChecklist() {
            const checklist = `高级功能测试清单：

□ 固定头部测试
  □ 页面滚动时头部保持固定
  □ 不同模式下头部固定正常
  □ 内容不被头部遮挡

□ 调试时间功能测试
  □ 点击"北京时间"可设置调试时间
  □ 调试模式下显示"调试时间"
  □ 时钟显示彩虹色渐变效果
  □ 系统时间基准正确切换
  □ 刷新页面调试模式保持
  □ 可正常退出测试模式

□ 线上配置加载测试
  □ "加载本地文件"按钮正常
  □ "加载线上配置"按钮正常
  □ URL输入和验证功能正常
  □ 成功加载线上配置文件
  □ 错误处理机制有效

□ 锚点跳转测试
  □ 添加产品后自动跳转
  □ 滚动动画效果正常
  □ 跳转到正确位置

□ 综合功能测试
  □ 所有新功能协同工作
  □ 不影响原有功能
  □ 界面响应正常`;

            alert(checklist);
        }
    </script>
</body>
</html>
